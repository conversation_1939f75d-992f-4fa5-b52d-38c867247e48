from sqlalchemy.orm import Session
import models, schemas
from typing import List, Optional
from sqlalchemy import desc, asc

# --- Player CRUD ---
def get_player(db: Session, player_id: int) -> Optional[models.Player]:
    return db.query(models.Player).filter(models.Player.id == player_id).first()

def get_player_by_gov_id(db: Session, governor_id: str) -> Optional[models.Player]:
    return db.query(models.Player).filter(models.Player.governor_id == governor_id).first()

def get_player_by_name(db: Session, name: str) -> Optional[models.Player]:
    # This might return multiple if names are not unique, consider refining
    return db.query(models.Player).filter(models.Player.name == name).first()

def get_players(db: Session, skip: int = 0, limit: int = 100) -> List[models.Player]:
    return db.query(models.Player).offset(skip).limit(limit).all()

def create_player(db: Session, player: schemas.PlayerCreate) -> models.Player:
    db_player = models.Player(
        name=player.name,
        governor_id=player.governor_id,
        alliance=player.alliance
    )
    db.add(db_player)
    db.commit()
    db.refresh(db_player)
    return db_player

def update_player(db: Session, player_id: int, player_update: schemas.PlayerCreate) -> Optional[models.Player]:
    db_player = get_player(db, player_id)
    if db_player:
        db_player.name = player_update.name
        db_player.governor_id = player_update.governor_id
        db_player.alliance = player_update.alliance
        db.commit()
        db.refresh(db_player)
    return db_player

# --- Scan CRUD ---
def get_scan(db: Session, scan_id: int) -> Optional[models.Scan]:
    return db.query(models.Scan).filter(models.Scan.id == scan_id).first()

def get_scan_by_name(db: Session, name: str) -> Optional[models.Scan]:
    return db.query(models.Scan).filter(models.Scan.name == name).first()

def get_scans(db: Session, skip: int = 0, limit: int = 100) -> List[models.Scan]:
    return db.query(models.Scan).order_by(desc(models.Scan.timestamp)).offset(skip).limit(limit).all()

def create_scan(db: Session, scan: schemas.ScanCreate) -> models.Scan:
    db_scan = models.Scan(
        name=scan.name,
        is_baseline=scan.is_baseline,
        kvk_id=scan.kvk_id,
        kvk_phase=scan.kvk_phase
    )
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)
    return db_scan

def get_latest_scan(db: Session) -> Optional[models.Scan]:
    return db.query(models.Scan).order_by(desc(models.Scan.timestamp)).first()

def get_baseline_scan(db: Session) -> Optional[models.Scan]:
    return db.query(models.Scan).filter(models.Scan.is_baseline == True).order_by(asc(models.Scan.timestamp)).first()

def get_baseline_scan_for_kvk(db: Session, kvk_id: int) -> Optional[models.Scan]:
    """Get the baseline scan for a specific KvK."""
    return db.query(models.Scan).filter(
        models.Scan.kvk_id == kvk_id,
        models.Scan.is_baseline == True
    ).first()

# --- PlayerStat CRUD ---
def create_player_stat(db: Session, player_stat: schemas.PlayerStatCreate, player_id: int, scan_id: int) -> models.PlayerStat:
    db_player_stat = models.PlayerStat(
        **player_stat.dict(exclude_unset=True, exclude={'player_name', 'governor_id', 'alliance'}),
        player_id=player_id,
        scan_id=scan_id
    )
    db.add(db_player_stat)
    db.commit()
    db.refresh(db_player_stat)
    return db_player_stat

def get_player_stat(db: Session, player_id: int, scan_id: int) -> Optional[models.PlayerStat]:
    return db.query(models.PlayerStat).filter(models.PlayerStat.player_id == player_id, models.PlayerStat.scan_id == scan_id).first()

def get_player_stats_for_scan(db: Session, scan_id: int) -> List[models.PlayerStat]:
    return db.query(models.PlayerStat).filter(models.PlayerStat.scan_id == scan_id).all()

def get_player_stats_for_player(db: Session, player_id: int) -> List[models.PlayerStat]:
    return db.query(models.PlayerStat).filter(models.PlayerStat.player_id == player_id).order_by(models.PlayerStat.scan_id).all()

# --- DeltaStat CRUD ---
def create_delta_stat(db: Session, delta_stat: schemas.DeltaStatCreate) -> models.DeltaStat:
    db_delta_stat = models.DeltaStat(**delta_stat.dict())
    db.add(db_delta_stat)
    db.commit()
    db.refresh(db_delta_stat)
    return db_delta_stat

def get_delta_stat(db: Session, player_id: int, start_scan_id: int, end_scan_id: int) -> Optional[models.DeltaStat]:
    return db.query(models.DeltaStat).filter(
        models.DeltaStat.player_id == player_id,
        models.DeltaStat.start_scan_id == start_scan_id,
        models.DeltaStat.end_scan_id == end_scan_id
    ).first()

def get_delta_stats_for_player(db: Session, player_id: int) -> List[models.DeltaStat]:
    return db.query(models.DeltaStat).filter(models.DeltaStat.player_id == player_id).all()

def get_delta_stats_for_scan_pair(db: Session, start_scan_id: int, end_scan_id: int) -> List[models.DeltaStat]:
    return db.query(models.DeltaStat).filter(
        models.DeltaStat.start_scan_id == start_scan_id,
        models.DeltaStat.end_scan_id == end_scan_id
    ).all()

def delete_delta_stats_for_scan_pair(db: Session, start_scan_id: int, end_scan_id: int) -> int:
    """Delete all delta stats for a specific scan pair and return count of deleted records."""
    deleted_count = db.query(models.DeltaStat).filter(
        models.DeltaStat.start_scan_id == start_scan_id,
        models.DeltaStat.end_scan_id == end_scan_id
    ).delete()
    db.commit()
    return deleted_count

# --- Parameter CRUD ---
def get_parameter(db: Session, parameter_id: int) -> Optional[models.Parameter]:
    return db.query(models.Parameter).filter(models.Parameter.id == parameter_id).first()

def get_parameter_by_name(db: Session, name: str) -> Optional[models.Parameter]:
    return db.query(models.Parameter).filter(models.Parameter.name == name).first()

def get_parameters(db: Session, skip: int = 0, limit: int = 100) -> List[models.Parameter]:
    return db.query(models.Parameter).offset(skip).limit(limit).all()

def create_parameter(db: Session, parameter: schemas.ParameterCreate) -> models.Parameter:
    db_parameter = models.Parameter(**parameter.dict())
    db.add(db_parameter)
    db.commit()
    db.refresh(db_parameter)
    return db_parameter

def update_parameter(db: Session, parameter_name: str, parameter_update: schemas.ParameterUpdate) -> Optional[models.Parameter]:
    db_parameter = get_parameter_by_name(db, parameter_name)
    if db_parameter:
        update_data = parameter_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_parameter, key, value)
        db.commit()
        db.refresh(db_parameter)
    return db_parameter

def delete_parameter(db: Session, parameter_name: str) -> Optional[models.Parameter]:
    db_parameter = get_parameter_by_name(db, parameter_name)
    if db_parameter:
        db.delete(db_parameter)
        db.commit()
    return db_parameter

# --- Utility to initialize parameters ---
def initialize_default_parameters(db: Session):
    default_params = {
        "min_kp_target": schemas.ParameterCreate(name="min_kp_target", value=50000000, description="Minimum Kill Points target for KvK participation."),
        "min_dead_target": schemas.ParameterCreate(name="min_dead_target", value=1000000, description="Minimum Dead Troops target for KvK participation."),
        "zeroed_power_drop_threshold_abs": schemas.ParameterCreate(name="zeroed_power_drop_threshold_abs", value=10000000, description="Absolute power drop to be considered for zeroing."),
        "zeroed_power_drop_threshold_pct": schemas.ParameterCreate(name="zeroed_power_drop_threshold_pct", value=0.20, description="Percentage power drop to be considered for zeroing (e.g., 0.20 for 20%)."),
        "significant_dead_troops_for_zeroed": schemas.ParameterCreate(name="significant_dead_troops_for_zeroed", value=500000, description="Minimum dead troops gained for a player with significant power drop to be marked 'zeroed'.")
    }
    for name, param_create in default_params.items():
        existing_param = get_parameter_by_name(db, name)
        if not existing_param:
            create_parameter(db, param_create)
    db.commit()

# --- User CRUD ---
def get_user(db: Session, user_id: int) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.email == email).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[models.User]:
    return db.query(models.User).offset(skip).limit(limit).all()

def create_user(db: Session, user_create: schemas.UserCreate) -> models.User:
    # The password in user_create should already be hashed
    db_user = models.User(
        username=user_create.username,
        email=user_create.email,
        hashed_password=user_create.password,  # This should be the hashed password
        is_active=True,
        is_admin=False
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, user_id: int, user_update: schemas.UserUpdate) -> Optional[models.User]:
    db_user = get_user(db, user_id)
    if db_user:
        update_data = user_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            if key == "password":
                # If password is being updated, it should be hashed
                setattr(db_user, "hashed_password", value)
            else:
                setattr(db_user, key, value)
        db.commit()
        db.refresh(db_user)
    return db_user

def delete_user(db: Session, user_id: int) -> Optional[models.User]:
    db_user = get_user(db, user_id)
    if db_user:
        db.delete(db_user)
        db.commit()
    return db_user