#!/usr/bin/env python3

import requests

def fix_kvk_scan():
    print('=== Fixing KvK Scan Association ===')
    
    # Check current state
    print('\n1. Checking current state...')
    kvks_response = requests.get('http://127.0.0.1:8000/api/kvks/')
    if kvks_response.status_code == 200:
        kvks = kvks_response.json()
        if kvks:
            kvk = kvks[0]  # Get the first KvK
            print(f'   Found KvK: {kvk["name"]} (ID: {kvk["id"]})')
            
            # Upload baseline scan for this KvK
            print(f'\n2. Uploading baseline scan for KvK {kvk["id"]}...')
            
            try:
                with open('../TOP300-2025-04-25-TOP300-25-04-2025-[5q3onfa5].xlsx', 'rb') as f:
                    files = {'file': f}
                    url = f'http://127.0.0.1:8000/api/scans/upload?scan_name=Baseline&is_baseline=true&kvk_id={kvk["id"]}'
                    
                    scan_response = requests.post(url, files=files)
                    print(f'   Upload Status: {scan_response.status_code}')
                    
                    if scan_response.status_code == 200:
                        scan = scan_response.json()
                        print(f'   ✅ Uploaded scan: {scan["name"]} (ID: {scan["id"]}, KvK ID: {scan.get("kvk_id", "None")})')
                        
                        # Test performance summary
                        print(f'\n3. Testing performance summary...')
                        perf_response = requests.get(f'http://127.0.0.1:8000/api/reports/kvk/performance_summary?kvk_id={kvk["id"]}&limit=3')
                        print(f'   Status: {perf_response.status_code}')
                        
                        if perf_response.status_code == 200:
                            data = perf_response.json()
                            print('   ✅ Performance summary works!')
                            print(f'   Total players: {data["summary_stats"]["total_players"]}')
                            print(f'   Baseline scan: {data["scan_period"]["baseline_scan_name"]}')
                            print(f'   Latest scan: {data["scan_period"]["latest_scan_name"]}')
                        else:
                            print(f'   ❌ Performance summary failed: {perf_response.text}')
                    else:
                        print(f'   ❌ Scan upload failed: {scan_response.text}')
                        
            except FileNotFoundError:
                print('   ❌ Excel file not found. Please make sure the file exists in the parent directory.')
            except Exception as e:
                print(f'   ❌ Error: {e}')
        else:
            print('   ❌ No KvKs found')
    else:
        print(f'   ❌ Failed to get KvKs: {kvks_response.text}')

if __name__ == "__main__":
    fix_kvk_scan()
