#!/usr/bin/env python3

from database import SessionLocal
import models

def reset_scans():
    """Delete all scans and related data to allow fresh upload with fixed Governor ID processing"""
    print('🗑️ Resetting all scan data...\n')
    
    db = SessionLocal()
    try:
        # Delete all delta stats first (foreign key constraints)
        delta_count = db.query(models.DeltaStat).count()
        db.query(models.DeltaStat).delete()
        print(f'✅ Deleted {delta_count} delta stats')
        
        # Delete all player stats
        player_stat_count = db.query(models.PlayerStat).count()
        db.query(models.PlayerStat).delete()
        print(f'✅ Deleted {player_stat_count} player stats')
        
        # Delete all scans
        scan_count = db.query(models.Scan).count()
        db.query(models.Scan).delete()
        print(f'✅ Deleted {scan_count} scans')
        
        # Delete all players (they will be recreated with correct Governor IDs)
        player_count = db.query(models.Player).count()
        db.query(models.Player).delete()
        print(f'✅ Deleted {player_count} players')
        
        # Commit all deletions
        db.commit()
        
        print('\n🎉 All scan data has been reset!')
        print('📤 You can now re-upload your scans with the fixed Governor ID processing.')
        
    except Exception as e:
        db.rollback()
        print(f'❌ Error resetting scans: {e}')
    finally:
        db.close()

if __name__ == "__main__":
    reset_scans()
