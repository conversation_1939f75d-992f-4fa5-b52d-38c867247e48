#!/usr/bin/env python3
"""
Test script to verify that the calculation fixes are working correctly.
This script tests the delta calculation logic and KvK performance metrics.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, engine
import models
import services
import crud
from datetime import datetime

def test_delta_calculations():
    """Test the fixed delta calculation logic."""
    print("=" * 60)
    print("TESTING DELTA CALCULATIONS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get latest scans for testing
        scans = crud.get_all_scans(db)
        if len(scans) < 2:
            print("❌ Need at least 2 scans to test delta calculations")
            return False
            
        baseline_scan = None
        latest_scan = None
        
        # Find baseline and latest scans
        for scan in scans:
            if scan.is_baseline:
                baseline_scan = scan
            if not latest_scan or scan.timestamp > latest_scan.timestamp:
                latest_scan = scan
                
        if not baseline_scan or not latest_scan:
            print("❌ Could not find baseline and latest scans")
            return False
            
        print(f"✅ Testing delta calculation between:")
        print(f"   Baseline: {baseline_scan.name} (ID: {baseline_scan.id})")
        print(f"   Latest:   {latest_scan.name} (ID: {latest_scan.id})")
        
        # Calculate delta stats
        delta_stats = services.calculate_delta_stats(db, latest_scan, baseline_scan)
        
        print(f"✅ Calculated {len(delta_stats)} delta stats")
        
        # Test specific calculation logic
        test_cases = []
        for ds in delta_stats[:5]:  # Test first 5 players
            if ds.player:
                # Get current and baseline stats
                current_stat = None
                baseline_stat = None
                
                for stat in latest_scan.player_stats:
                    if stat.player_id == ds.player_id:
                        current_stat = stat
                        break
                        
                for stat in baseline_scan.player_stats:
                    if stat.player_id == ds.player_id:
                        baseline_stat = stat
                        break
                
                if current_stat and baseline_stat:
                    # Verify calculations
                    expected_power_delta = (current_stat.power or 0) - (baseline_stat.power or 0)
                    expected_kp_delta = (current_stat.total_kill_points or 0) - (baseline_stat.total_kill_points or 0)
                    expected_dead_delta = (current_stat.dead_troops or 0) - (baseline_stat.dead_troops or 0)
                    
                    # Ensure KP delta is never negative
                    if expected_kp_delta < 0:
                        expected_kp_delta = 0
                    
                    test_case = {
                        'player_name': ds.player.name,
                        'power_delta_calculated': ds.power_delta,
                        'power_delta_expected': expected_power_delta,
                        'kp_delta_calculated': ds.kill_points_delta,
                        'kp_delta_expected': expected_kp_delta,
                        'dead_delta_calculated': ds.dead_troops_delta,
                        'dead_delta_expected': expected_dead_delta,
                        'kp_per_dead': getattr(ds, 'kp_per_dead', 0.0),
                        'is_zeroed': getattr(ds, 'is_zeroed', False)
                    }
                    test_cases.append(test_case)
        
        # Verify calculations
        all_correct = True
        for case in test_cases:
            power_correct = case['power_delta_calculated'] == case['power_delta_expected']
            kp_correct = case['kp_delta_calculated'] == case['kp_delta_expected']
            dead_correct = case['dead_delta_calculated'] == case['dead_delta_expected']
            
            if power_correct and kp_correct and dead_correct:
                print(f"✅ {case['player_name']}: All calculations correct")
            else:
                print(f"❌ {case['player_name']}: Calculation mismatch")
                if not power_correct:
                    print(f"   Power: Expected {case['power_delta_expected']}, Got {case['power_delta_calculated']}")
                if not kp_correct:
                    print(f"   KP: Expected {case['kp_delta_expected']}, Got {case['kp_delta_calculated']}")
                if not dead_correct:
                    print(f"   Dead: Expected {case['dead_delta_expected']}, Got {case['dead_delta_calculated']}")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Error testing delta calculations: {str(e)}")
        return False
    finally:
        db.close()

def test_kvk_performance_calculations():
    """Test KvK performance calculation fixes."""
    print("\n" + "=" * 60)
    print("TESTING KVK PERFORMANCE CALCULATIONS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get active KvKs
        kvks = crud.get_all_kvks(db)
        if not kvks:
            print("❌ No KvKs found for testing")
            return False
            
        # Test the first KvK
        kvk = kvks[0]
        print(f"✅ Testing KvK performance for: {kvk.name}")
        
        # Get KvK performance summary
        performance_data = services.get_kvk_performance_summary(db, kvk.id, limit=10)
        
        if not performance_data or 'performance_data' not in performance_data:
            print("❌ No performance data returned")
            return False
            
        players = performance_data['performance_data']
        print(f"✅ Retrieved performance data for {len(players)} players")
        
        # Test calculation logic
        calculation_issues = []
        for player in players[:5]:  # Test first 5 players
            # Check for negative KP deltas (should not happen)
            if player.get('kp_delta', 0) < 0:
                calculation_issues.append(f"Player {player['player_name']} has negative KP delta: {player['kp_delta']}")
            
            # Check efficiency calculations
            kp_per_dead = player.get('kp_per_dead', 0)
            if player.get('dead_delta', 0) > 0:
                expected_kp_per_dead = player.get('kp_delta', 0) / player.get('dead_delta', 1)
                if abs(kp_per_dead - expected_kp_per_dead) > 0.01:  # Allow small floating point differences
                    calculation_issues.append(f"Player {player['player_name']} KP per dead mismatch: Expected {expected_kp_per_dead:.2f}, Got {kp_per_dead:.2f}")
            
            # Check underperforming logic
            needs_improvement = player.get('needs_improvement', False)
            kp_delta = player.get('kp_delta', 0)
            is_new_player = player.get('is_new_player', False)
            
            # Underperforming should be true only if KP delta is 0 and not a new player
            expected_needs_improvement = (kp_delta == 0) and not is_new_player
            if needs_improvement != expected_needs_improvement:
                calculation_issues.append(f"Player {player['player_name']} underperforming flag incorrect: Expected {expected_needs_improvement}, Got {needs_improvement}")
        
        if calculation_issues:
            print("❌ Found calculation issues:")
            for issue in calculation_issues:
                print(f"   {issue}")
            return False
        else:
            print("✅ All KvK performance calculations are correct")
            return True
            
    except Exception as e:
        print(f"❌ Error testing KvK performance calculations: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def test_general_performance_calculations():
    """Test general performance calculation fixes."""
    print("\n" + "=" * 60)
    print("TESTING GENERAL PERFORMANCE CALCULATIONS")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get general performance summary
        performance_data = services.get_general_performance_summary(db, limit=10)
        
        if not performance_data or 'performance_data' not in performance_data:
            print("❌ No general performance data returned")
            return False
            
        players = performance_data['performance_data']
        summary_stats = performance_data['summary_stats']
        
        print(f"✅ Retrieved general performance data for {len(players)} players")
        
        # Test summary statistics
        expected_total_power_gain = sum(p['power_delta'] for p in players if p['power_delta'] > 0)
        expected_total_power_loss = sum(abs(p['power_delta']) for p in players if p['power_delta'] < 0)
        expected_net_power_change = sum(p['power_delta'] for p in players)
        
        actual_total_power_gain = summary_stats.get('total_power_gain', 0)
        actual_total_power_loss = summary_stats.get('total_power_loss', 0)
        actual_net_power_change = summary_stats.get('net_power_change', 0)
        
        summary_correct = True
        if actual_total_power_gain != expected_total_power_gain:
            print(f"❌ Total power gain mismatch: Expected {expected_total_power_gain}, Got {actual_total_power_gain}")
            summary_correct = False
            
        if actual_total_power_loss != expected_total_power_loss:
            print(f"❌ Total power loss mismatch: Expected {expected_total_power_loss}, Got {actual_total_power_loss}")
            summary_correct = False
            
        if actual_net_power_change != expected_net_power_change:
            print(f"❌ Net power change mismatch: Expected {expected_net_power_change}, Got {actual_net_power_change}")
            summary_correct = False
        
        if summary_correct:
            print("✅ All general performance summary statistics are correct")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Error testing general performance calculations: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

def main():
    """Run all calculation tests."""
    print("RISE OF KINGDOMS CALCULATION FIXES VERIFICATION")
    print("=" * 60)
    print("Testing backend calculation fixes...")
    
    # Run all tests
    tests = [
        ("Delta Calculations", test_delta_calculations),
        ("KvK Performance Calculations", test_kvk_performance_calculations),
        ("General Performance Calculations", test_general_performance_calculations)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All calculation fixes are working correctly!")
        return True
    else:
        print("⚠️  Some calculation issues remain. Please review the failed tests.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
