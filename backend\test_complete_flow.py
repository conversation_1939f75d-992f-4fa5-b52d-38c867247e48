#!/usr/bin/env python3

import requests
import time
import os

def test_complete_flow():
    """Test the complete flow: Create KvK -> Upload Scan -> Test Performance Summary"""
    base_url = "http://127.0.0.1:8000/api"

    print("🧪 Testing Complete KvK Flow...")

    # Step 1: Create KvK
    print("\n1. Creating KvK...")
    timestamp = int(time.time())
    kvk_data = {
        'name': f'Test KvK {timestamp}',
        'start_date': '2024-01-01T00:00:00',
        'season': 1,
        'status': 'upcoming'
    }

    kvk_response = requests.post(f"{base_url}/kvks/", json=kvk_data)
    print(f"   Status: {kvk_response.status_code}")

    if kvk_response.status_code != 201:
        print(f"   ❌ KvK creation failed: {kvk_response.text}")
        return

    kvk = kvk_response.json()
    kvk_id = kvk['id']
    print(f"   ✅ Created KvK: {kvk['name']} (ID: {kvk_id}, Status: {kvk['status']})")

    # Step 2: Upload baseline scan
    print(f"\n2. Uploading baseline scan to KvK {kvk_id}...")
    excel_file = "../TOP300-2025-04-25-TOP300-25-04-2025-[5q3onfa5].xlsx"

    if not os.path.exists(excel_file):
        print(f"   ❌ Excel file not found: {excel_file}")
        return

    with open(excel_file, 'rb') as f:
        files = {'file': f}
        url = f"{base_url}/scans/upload?scan_name=Baseline Scan&is_baseline=true&kvk_id={kvk_id}"

        scan_response = requests.post(url, files=files)
        print(f"   Status: {scan_response.status_code}")

        if scan_response.status_code != 200:
            print(f"   ❌ Scan upload failed: {scan_response.text}")
            return

        scan = scan_response.json()
        print(f"   ✅ Uploaded scan: {scan['name']} (ID: {scan['id']}, KvK ID: {scan.get('kvk_id', 'None')})")

    # Step 3: Test performance summary
    print(f"\n3. Testing performance summary for KvK {kvk_id}...")
    perf_response = requests.get(f"{base_url}/reports/kvk/performance_summary?kvk_id={kvk_id}&limit=5")
    print(f"   Status: {perf_response.status_code}")

    if perf_response.status_code == 200:
        data = perf_response.json()
        print("   ✅ Performance summary successful!")
        print(f"   📊 Total players: {data['summary_stats']['total_players']}")
        print(f"   📅 Baseline scan: {data['scan_period']['baseline_scan_name']}")
        print(f"   📅 Latest scan: {data['scan_period']['latest_scan_name']}")
        print(f"   👥 Sample players returned: {len(data['performance_data'])}")

        if data['performance_data']:
            player = data['performance_data'][0]
            print(f"   🎯 Sample player: {player['player_name']}")
            print(f"      - Power: {player['current_power']:,}")
            print(f"      - Kill Points: {player['current_kp']:,}")
            print(f"      - Dead Troops: {player['current_dead']:,}")
            print(f"      - KP Delta: {player['kp_delta']} (baseline only)")
            print(f"      - Power Delta: {player['power_delta']} (baseline only)")
    else:
        print(f"   ❌ Performance summary failed: {perf_response.text}")
        return

    # Step 4: Test active KvK endpoint
    print(f"\n4. Testing active KvK endpoint...")
    active_response = requests.get(f"{base_url}/kvks/active")
    print(f"   Status: {active_response.status_code}")

    if active_response.status_code == 200:
        active_kvk = active_response.json()
        print(f"   ✅ Active KvK: {active_kvk['name']} (ID: {active_kvk['id']})")
    else:
        print(f"   ❌ No active KvK: {active_response.text}")

    print("\n🎉 Complete flow test finished!")

if __name__ == "__main__":
    test_complete_flow()
