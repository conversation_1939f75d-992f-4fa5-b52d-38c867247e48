#!/usr/bin/env python3

import requests
import time

def test_performance_endpoints():
    """Test both dashboard and performance endpoints"""
    base_url = 'http://localhost:8000/api'
    
    print('🧪 Testing Performance Endpoints...\n')
    
    # Test 1: Dashboard endpoint
    print('1. Testing Dashboard:')
    try:
        dashboard_response = requests.get(f'{base_url}/dashboard/')
        print(f'   Status: {dashboard_response.status_code}')
        if dashboard_response.status_code == 200:
            data = dashboard_response.json()
            print(f'   ✅ SUCCESS!')
            print(f'   Total KP: {data.get("total_kill_points", 0):,}')
            print(f'   KP Gain: {data.get("kill_points_gain", 0):,}')
            print(f'   Total Power: {data.get("total_power", 0):,}')
            print(f'   Player Count: {data.get("player_count", 0)}')
        else:
            print(f'   ❌ Error: {dashboard_response.text}')
    except Exception as e:
        print(f'   Exception: {e}')
    
    # Test 2: General Performance Summary (NEW)
    print('\n2. Testing General Performance Summary:')
    try:
        perf_response = requests.get(f'{base_url}/reports/general/performance_summary?limit=5')
        print(f'   Status: {perf_response.status_code}')
        if perf_response.status_code == 200:
            data = perf_response.json()
            print(f'   ✅ SUCCESS!')
            print(f'   Total players: {data["summary_stats"]["total_players"]}')
            print(f'   Total KP gain: {data["summary_stats"]["total_kp_gain"]:,}')
            print(f'   Top performers: {data["summary_stats"]["top_performers"]}')
            print(f'   New players: {data["summary_stats"]["new_players"]}')
            print(f'   Players left: {data["summary_stats"]["players_left"]}')
            print(f'   Performance data returned: {len(data["performance_data"])} players')
            
            if data["performance_data"]:
                print('\n   📊 Top 3 Performers:')
                for i, player in enumerate(data["performance_data"][:3]):
                    print(f'   {i+1}. {player["player_name"]} - KP: {player["kill_points_gain"]:,}, Power: {player["power_gain"]:,}')
        else:
            print(f'   ❌ Error: {perf_response.text}')
    except Exception as e:
        print(f'   Exception: {e}')
    
    # Test 3: KvK Performance Summary (should fail - no KvK)
    print('\n3. Testing KvK Performance Summary (should fail):')
    try:
        kvk_response = requests.get(f'{base_url}/reports/kvk/performance_summary?kvk_id=1&limit=5')
        print(f'   Status: {kvk_response.status_code}')
        if kvk_response.status_code == 200:
            print(f'   ✅ Unexpected success!')
        else:
            print(f'   ❌ Expected error: {kvk_response.status_code}')
    except Exception as e:
        print(f'   Exception: {e}')

if __name__ == "__main__":
    test_performance_endpoints()
