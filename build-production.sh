#!/bin/bash

# Production Build Script for Kingdom 2358 Data Portal
# This script builds the application for production deployment

set -e  # Exit on any error

echo "🏗️ Building Kingdom 2358 Data Portal for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

print_status "Node.js version check passed ✓"

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf frontend/dist
rm -rf frontend/build

# Install frontend dependencies
print_status "Installing frontend dependencies..."
cd frontend
npm ci --production=false

# Build frontend for production
print_status "Building frontend for production..."
npm run build

# Check if build was successful
if [ -d "dist" ]; then
    print_success "Frontend built successfully!"
    print_status "Build output: frontend/dist/"
    
    # Display build statistics
    echo ""
    echo "📊 BUILD STATISTICS:"
    echo "==================="
    du -sh dist/
    echo "Files created:"
    find dist/ -type f | wc -l
    echo ""
    
    print_success "Production build completed! 🎉"
    echo ""
    echo "📁 Next steps:"
    echo "1. Upload the 'frontend/dist' folder to your web server"
    echo "2. Configure your web server to serve the static files"
    echo "3. Ensure the backend API is running and accessible"
    echo "4. Test the production deployment"
else
    print_error "Build failed! No dist directory found."
    exit 1
fi
