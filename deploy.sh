#!/bin/bash

# Production Deployment Script for Data Website
# This script sets up the application for production deployment

set -e  # Exit on any error

echo "🚀 Starting production deployment setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (not recommended for production)
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root is not recommended for production deployment"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/logs
mkdir -p backend/uploads
mkdir -p backend/backups
mkdir -p frontend/dist

# Set proper permissions
print_status "Setting directory permissions..."
chmod 755 backend/logs
chmod 755 backend/uploads
chmod 755 backend/backups

# Backend setup
print_status "Setting up backend..."
cd backend

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    print_error "Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

print_success "Python version check passed: $python_version"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
print_status "Installing Python dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
    print_warning "Please edit .env file with your production settings!"
fi

# Validate environment variables
print_status "Validating environment configuration..."
source .env

if [ "$SECRET_KEY" = "your-super-secret-key-change-this-in-production" ] || [ "$SECRET_KEY" = "development-secret-key-change-in-production-12345" ]; then
    print_error "SECRET_KEY must be changed for production!"
    print_status "Generating new SECRET_KEY..."
    new_secret=$(python3 -c "import secrets; print(secrets.token_hex(32))")
    sed -i "s/SECRET_KEY=.*/SECRET_KEY=$new_secret/" .env
    print_success "New SECRET_KEY generated and saved to .env"
fi

# Database setup
print_status "Setting up database..."
if [ ! -f "data_website.db" ]; then
    print_status "Database will be created on first run"
else
    print_status "Existing database found"
fi

# Test backend startup
print_status "Testing backend startup..."
timeout 10s python3 -c "
import sys
sys.path.append('.')
from main import app
from database import engine
from sqlalchemy import text

# Test database connection
with engine.connect() as conn:
    conn.execute(text('SELECT 1'))
    
print('Backend startup test passed')
" || {
    print_error "Backend startup test failed"
    exit 1
}

print_success "Backend setup completed"

# Frontend setup
cd ../frontend
print_status "Setting up frontend..."

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    print_error "Node.js is required but not installed"
    exit 1
fi

# Check Node.js version
node_version=$(node --version | cut -d'v' -f2 | cut -d. -f1)
if [ "$node_version" -lt 16 ]; then
    print_error "Node.js 16+ is required. Current version: $(node --version)"
    exit 1
fi

print_success "Node.js version check passed: $(node --version)"

# Install dependencies
print_status "Installing Node.js dependencies..."
npm install

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning "Frontend .env file not found. Creating default..."
    echo "VITE_API_URL=http://localhost:8000" > .env
    echo "NODE_ENV=production" >> .env
fi

# Build frontend for production
print_status "Building frontend for production..."
npm run build

print_success "Frontend setup completed"

# Go back to root directory
cd ..

# Create systemd service files (optional)
print_status "Creating systemd service files..."

# Backend service
cat > data-website-backend.service << EOF
[Unit]
Description=Data Website Backend API
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)/backend
Environment=PATH=$(pwd)/backend/venv/bin
ExecStart=$(pwd)/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# Frontend service (using a simple HTTP server)
cat > data-website-frontend.service << EOF
[Unit]
Description=Data Website Frontend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)/frontend/dist
ExecStart=/usr/bin/python3 -m http.server 3000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

print_success "Systemd service files created"

# Create nginx configuration (optional)
print_status "Creating nginx configuration template..."

cat > nginx-data-website.conf << EOF
server {
    listen 80;
    server_name your-domain.com;  # Change this to your domain

    # Frontend
    location / {
        root $(pwd)/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Increase timeout for large file uploads
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        client_max_body_size 100M;
    }

    # Health checks
    location /api/health/ {
        proxy_pass http://127.0.0.1:8000;
        access_log off;
    }
}
EOF

print_success "Nginx configuration template created"

# Final instructions
echo
print_success "🎉 Production deployment setup completed!"
echo
print_status "Next steps:"
echo "1. Review and update .env files with your production settings"
echo "2. Set up SSL certificates (recommended: Let's Encrypt)"
echo "3. Configure your web server (nginx configuration provided)"
echo "4. Set up systemd services (service files provided)"
echo "5. Configure firewall rules"
echo "6. Set up monitoring and backups"
echo
print_status "To start the application manually:"
echo "Backend:  cd backend && source venv/bin/activate && uvicorn main:app --host 0.0.0.0 --port 8000"
echo "Frontend: cd frontend && npm run preview"
echo
print_status "To install systemd services:"
echo "sudo cp data-website-*.service /etc/systemd/system/"
echo "sudo systemctl daemon-reload"
echo "sudo systemctl enable data-website-backend data-website-frontend"
echo "sudo systemctl start data-website-backend data-website-frontend"
echo
print_warning "Remember to:"
echo "- Change default passwords and secrets"
echo "- Set up regular database backups"
echo "- Configure log rotation"
echo "- Set up monitoring alerts"
echo "- Test the deployment thoroughly"
