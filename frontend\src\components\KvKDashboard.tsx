import React, { useState, useEffect, useMemo } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { KvKData, ScanData, PlayerScanData } from '../types/dataTypes';
import { formatLargeNumber } from '../utils/formatters';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getKvKPerformanceSummary, getKvKSummary } from '../api/api';
import KvKPlayerTable from './KvKPlayerTable';
import { FaMedal, FaAward } from 'react-icons/fa';
import {
  FaShieldAlt,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy,
  FaClock,
  FaFire,
  FaChartLine,
  FaEye,
  FaUsers,
  FaCrown
} from 'react-icons/fa';

interface KvKDashboardProps {
  kvk: KvKData;
  latestScan?: ScanData;
  previousScan?: ScanData;
  baselineScan?: ScanData;
}

const KvKDashboard: React.FC<KvKDashboardProps> = ({
  kvk,
  latestScan,
  previousScan,
  baselineScan
}) => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [timeUntilUpdate, setTimeUntilUpdate] = useState<string>('00:00');
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'killpoints' | 'power' | 'deads'>('killpoints');

  // Fetch KvK summary data for live stats
  const { data: summaryData } = useQuery({
    queryKey: ['kvkSummary', kvk.id],
    queryFn: () => getKvKSummary(kvk.id),
    enabled: !!kvk.id,
    refetchInterval: 30000, // Refetch every 30 seconds for live updates
    staleTime: 10000,
  });

  // Fetch KvK performance data for player rankings
  const { data: performanceData } = useQuery({
    queryKey: ['kvkPerformance', kvk.id],
    queryFn: () => getKvKPerformanceSummary(kvk.id, 10), // Get top 10 players
    enabled: !!kvk.id,
    refetchInterval: 60000, // Refetch every minute
    staleTime: 30000,
  });

  // Use live summary data for calculations
  const { totalKillPoints, killPointsGain, last24hGain, hasLiveData } = useMemo(() => {
    if (summaryData && summaryData.has_data) {
      // Use live API data
      return {
        totalKillPoints: summaryData.total_kill_points,
        killPointsGain: summaryData.total_kill_points,
        last24hGain: 0, // TODO: Add 24h tracking to API
        hasLiveData: true
      };
    }

    // Fallback to scan data calculations
    const total = latestScan?.players?.reduce((sum, player) => sum + (player.killPoints || 0), 0) || 0;
    const previousTotal = previousScan?.players?.reduce((sum, player) => sum + (player.killPoints || 0), 0) || 0;
    const baselineTotal = baselineScan?.players?.reduce((sum, player) => sum + (player.killPoints || 0), 0) || 0;

    // If we only have baseline scan, show baseline values
    if (!latestScan || !latestScan.players || latestScan.id === baselineScan?.id) {
      return {
        totalKillPoints: baselineTotal,
        killPointsGain: 0, // No gain to show yet
        last24hGain: 0,
        hasLiveData: false
      };
    }

    return {
      totalKillPoints: total,
      killPointsGain: Math.max(0, total - baselineTotal), // Ensure non-negative
      last24hGain: total - previousTotal,
      hasLiveData: false
    };
  }, [summaryData, latestScan, previousScan, baselineScan]);

  const { t45CasualtiesGain, currentT45Kills } = useMemo(() => {
    if (summaryData && summaryData.has_data) {
      // Use live API data
      return {
        t45CasualtiesGain: summaryData.total_t45_kills,
        currentT45Kills: summaryData.total_t45_kills
      };
    }

    // Fallback to scan data calculations
    const total = latestScan?.players?.reduce((sum, player) => sum + (player.t45Kills || 0), 0) || 0;
    const baseline = baselineScan?.players?.reduce((sum, player) => sum + (player.t45Kills || 0), 0) || 0;

    // If we only have baseline scan (no latest scan or they're the same), show baseline values
    if (!latestScan || !latestScan.players || latestScan.id === baselineScan?.id) {
      return {
        t45CasualtiesGain: 0, // No gain to show yet
        currentT45Kills: baseline
      };
    }

    return {
      t45CasualtiesGain: Math.max(0, total - baseline), // Ensure non-negative
      currentT45Kills: total
    };
  }, [summaryData, latestScan, baselineScan]);

  const { deadsGain, currentDeads } = useMemo(() => {
    if (summaryData && summaryData.has_data) {
      // Use live API data
      return {
        deadsGain: summaryData.total_dead_troops,
        currentDeads: summaryData.total_dead_troops
      };
    }

    // Fallback to scan data calculations
    const total = latestScan?.players?.reduce((sum, player) => sum + (player.deads || 0), 0) || 0;
    const baseline = baselineScan?.players?.reduce((sum, player) => sum + (player.deads || 0), 0) || 0;

    // If we only have baseline scan (no latest scan or they're the same), show baseline values
    if (!latestScan || !latestScan.players || latestScan.id === baselineScan?.id) {
      return {
        deadsGain: 0, // No gain to show yet
        currentDeads: baseline
      };
    }

    return {
      deadsGain: Math.max(0, total - baseline), // Ensure non-negative
      currentDeads: total
    };
  }, [summaryData, latestScan, baselineScan]);

  // Process performance data for player rankings
  const topPlayers = useMemo(() => {
    if (!performanceData?.performance_data) return [];

    // Check if we're in baseline-only mode (all deltas are 0)
    const isBaselineOnly = performanceData.performance_data.every((player: any) =>
      (player.kp_delta || 0) === 0 && (player.power_delta || 0) === 0 && (player.dead_delta || 0) === 0
    );

    // Transform API data to match PlayerScanData interface
    const players: PlayerScanData[] = performanceData.performance_data.map((player: any) => ({
      governorId: player.governor_id || '',
      name: player.player_name || player.name || '',
      alliance: player.alliance || '',
      // Use current values for baseline-only mode, deltas for gains view
      power: isBaselineOnly ? (player.current_power || 0) : (player.power_delta || 0),
      killPoints: isBaselineOnly ? (player.current_kp || 0) : (player.kp_delta || 0),
      deads: isBaselineOnly ? (player.current_dead || 0) : (player.dead_delta || 0),
      t1Kills: 0,
      t2Kills: 0,
      t3Kills: 0,
      t4Kills: isBaselineOnly ? (player.current_t4_kills || 0) : (player.t4_delta || 0),
      t5Kills: isBaselineOnly ? (player.current_t5_kills || 0) : (player.t5_delta || 0),
      totalKills: 0,
      t45Kills: isBaselineOnly ?
        ((player.current_t4_kills || 0) + (player.current_t5_kills || 0)) :
        (player.t45_delta || (player.t4_delta || 0) + (player.t5_delta || 0)),
      ranged: 0,
      rssGathered: 0,
      rssAssisted: 0,
      helps: 0
    }));

    // Sort by the active tab metric
    if (activeTab === 'killpoints') {
      return players.sort((a, b) => (b.killPoints || 0) - (a.killPoints || 0));
    } else if (activeTab === 'power') {
      return players.sort((a, b) => (b.power || 0) - (a.power || 0));
    } else if (activeTab === 'deads') {
      return players.sort((a, b) => (b.deads || 0) - (a.deads || 0));
    }
    return players;
  }, [performanceData, activeTab]);

  // Update the countdown timer
  useEffect(() => {
    if (!latestScan) return;

    // Set last update time
    const scanDate = new Date(latestScan.date);
    setLastUpdateTime(scanDate.toLocaleString());

    // Calculate next update time (hourly)
    const nextUpdate = new Date(scanDate);
    nextUpdate.setHours(nextUpdate.getHours() + 1);

    const updateTimer = () => {
      const now = new Date();
      const diff = nextUpdate.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeUntilUpdate('00:00');
        return;
      }

      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeUntilUpdate(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTimer();
    const timerId = setInterval(updateTimer, 1000);

    return () => clearInterval(timerId);
  }, [latestScan]);

  const handleCardClick = () => {
    if (kvk && kvk.id) {
      navigate(`/kvk/${kvk.id}`);
    }
  };

  return (
    <div
      className={`group relative overflow-hidden rounded-3xl shadow-2xl transition-all duration-300 ease-in-out cursor-pointer transform hover:scale-102 ${
        theme === 'light'
          ? 'bg-gradient-to-br from-white to-gray-50 hover:shadow-3xl'
          : 'bg-gradient-to-br from-gray-800 to-gray-900 hover:shadow-3xl'
      }`}
      onClick={handleCardClick}
      style={{ transform: 'translateZ(0)' }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20"></div>
      </div>

      {/* Enhanced KvK Header */}
      <div className={`relative px-8 py-6 ${
        theme === 'light'
          ? 'bg-gradient-to-r from-blue-600 to-indigo-600'
          : 'bg-gradient-to-r from-blue-700 to-indigo-700'
      } text-white`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-3 rounded-xl bg-white/20 backdrop-blur-sm mr-4">
              <FaShieldAlt className="text-2xl" />
            </div>
            <div>
              <h3 className="text-2xl font-bold mb-1">
                {kvk.name}
              </h3>
              <p className="text-blue-100 text-lg">
                Season {kvk.season}
              </p>
            </div>
          </div>

          <div className="text-right">
            <div className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold ${
              kvk.status === 'active'
                ? 'bg-green-500/20 text-green-100'
                : kvk.status === 'completed'
                ? 'bg-gray-500/20 text-gray-100'
                : 'bg-yellow-500/20 text-yellow-100'
            }`}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                kvk.status === 'active' ? 'bg-green-400' : kvk.status === 'completed' ? 'bg-gray-400' : 'bg-yellow-400'
              }`}></div>
              {kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}
            </div>
            <div className="flex items-center mt-2 text-blue-100 text-sm">
              <FaEye className="mr-1" />
              <span>Click to view details</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Body */}
      <div className="relative p-8 space-y-6">
        {/* Kill Points Card */}
        <div className={`relative overflow-hidden p-6 rounded-2xl transition-all duration-300 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-red-50 to-pink-100 hover:from-red-100 hover:to-pink-200'
            : 'bg-gradient-to-br from-red-900/30 to-pink-900/30 hover:from-red-900/40 hover:to-pink-900/40'
        }`}>
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-red-500 text-white' : 'bg-red-600 text-white'
              }`}>
                <FaCrosshairs className="text-xl" />
              </div>
              <div>
                <h4 className={`text-lg font-bold ${
                  theme === 'light' ? 'text-red-800' : 'text-red-300'
                }`}>
                  {hasLiveData || (latestScan && baselineScan && latestScan.id !== baselineScan.id)
                    ? 'Kill Points Gain'
                    : 'Total Kill Points'
                  }
                </h4>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-red-600' : 'text-red-400'
                }`}>
                  {hasLiveData
                    ? 'Live data from API'
                    : (latestScan && baselineScan && latestScan.id !== baselineScan.id
                      ? 'Since baseline scan'
                      : 'Current total from baseline'
                    )
                  }
                </p>
              </div>
            </div>

            {latestScan && baselineScan && latestScan.id !== baselineScan.id && (
              <div className="flex items-center px-3 py-1 rounded-full bg-orange-500/20 text-orange-600">
                <FaFire className="text-xs mr-1" />
                <span className="text-xs font-semibold">
                  24h: {last24hGain > 0 ? '+' : ''}{formatLargeNumber(last24hGain)}
                </span>
              </div>
            )}
          </div>

          <div className={`text-4xl font-bold mb-2 ${
            theme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            {summaryData && !summaryData.has_data
              ? (typeof summaryData.total_kill_points === 'string' ? summaryData.total_kill_points : 'TBD')
              : (hasLiveData || (latestScan && baselineScan && latestScan.id !== baselineScan.id)
                ? `+${formatLargeNumber(killPointsGain)}`
                : formatLargeNumber(totalKillPoints)
              )
            }
          </div>

          <div className={`text-sm ${
            theme === 'light' ? 'text-gray-600' : 'text-gray-400'
          }`}>
            {summaryData && !summaryData.has_data
              ? 'No data available yet'
              : (hasLiveData
                ? `Live: ${formatLargeNumber(killPointsGain, true)}`
                : (latestScan && baselineScan && latestScan.id !== baselineScan.id
                  ? `Approximately ${formatLargeNumber(killPointsGain, true)}`
                  : 'Baseline measurement'
                )
              )
            }
          </div>
        </div>

        {/* T4-5 Kills & Deads Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* T4-5 Kills Card */}
          <div className={`relative overflow-hidden p-6 rounded-2xl transition-all duration-300 ${
            theme === 'light'
              ? 'bg-gradient-to-br from-purple-50 to-violet-100 hover:from-purple-100 hover:to-violet-200'
              : 'bg-gradient-to-br from-purple-900/30 to-violet-900/30 hover:from-purple-900/40 hover:to-violet-900/40'
          }`}>
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-purple-500 text-white' : 'bg-purple-600 text-white'
              }`}>
                <FaTrophy className="text-xl" />
              </div>
              <div>
                <h4 className={`text-lg font-bold ${
                  theme === 'light' ? 'text-purple-800' : 'text-purple-300'
                }`}>
                  T4-5 Kills
                </h4>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-purple-600' : 'text-purple-400'
                }`}>
                  Elite eliminations
                </p>
              </div>
            </div>

            <div className={`text-3xl font-bold mb-2 ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              {summaryData && !summaryData.has_data
                ? (typeof summaryData.total_t45_kills === 'string' ? summaryData.total_t45_kills : 'TBD')
                : (hasLiveData || (latestScan && baselineScan && latestScan.id !== baselineScan.id)
                  ? `+${formatLargeNumber(t45CasualtiesGain)}`
                  : formatLargeNumber(currentT45Kills)
                )
              }
            </div>

            <div className={`text-sm ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}>
              {summaryData && !summaryData.has_data
                ? 'No data available yet'
                : (hasLiveData
                  ? 'Live data'
                  : (latestScan && baselineScan && latestScan.id !== baselineScan.id
                    ? 'Gain from baseline'
                    : 'Current total'
                  )
                )
              }
            </div>
          </div>

          {/* Deads Card */}
          <div className={`relative overflow-hidden p-6 rounded-2xl transition-all duration-300 ${
            theme === 'light'
              ? 'bg-gradient-to-br from-yellow-50 to-orange-100 hover:from-yellow-100 hover:to-orange-200'
              : 'bg-gradient-to-br from-yellow-900/30 to-orange-900/30 hover:from-yellow-900/40 hover:to-orange-900/40'
          }`}>
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-yellow-500 text-white' : 'bg-yellow-600 text-white'
              }`}>
                <FaSkullCrossbones className="text-xl" />
              </div>
              <div>
                <h4 className={`text-lg font-bold ${
                  theme === 'light' ? 'text-yellow-800' : 'text-yellow-300'
                }`}>
                  Dead Troops
                </h4>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'
                }`}>
                  Casualties sustained
                </p>
              </div>
            </div>

            <div className={`text-3xl font-bold mb-2 ${
              theme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              {summaryData && !summaryData.has_data
                ? (typeof summaryData.total_dead_troops === 'string' ? summaryData.total_dead_troops : 'TBD')
                : (hasLiveData || (latestScan && baselineScan && latestScan.id !== baselineScan.id)
                  ? `+${formatLargeNumber(deadsGain)}`
                  : formatLargeNumber(currentDeads)
                )
              }
            </div>

            <div className={`text-sm ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}>
              {summaryData && !summaryData.has_data
                ? 'No data available yet'
                : (hasLiveData
                  ? 'Live data'
                  : (latestScan && baselineScan && latestScan.id !== baselineScan.id
                    ? 'Gain from baseline'
                    : 'Current total'
                  )
                )
              }
            </div>
          </div>
        </div>

        {/* Enhanced Update Timer */}
        <div className={`relative overflow-hidden p-6 rounded-2xl transition-all duration-300 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-green-50 to-emerald-100 hover:from-green-100 hover:to-emerald-200'
            : 'bg-gradient-to-br from-green-900/30 to-emerald-900/30 hover:from-green-900/40 hover:to-emerald-900/40'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-green-500 text-white' : 'bg-green-600 text-white'
              }`}>
                <FaClock className="text-xl" />
              </div>
              <div>
                <h4 className={`text-lg font-bold ${
                  theme === 'light' ? 'text-green-800' : 'text-green-300'
                }`}>
                  Next Update In
                </h4>
                <p className={`text-sm ${
                  theme === 'light' ? 'text-green-600' : 'text-green-400'
                }`}>
                  Auto-refresh every hour
                </p>
              </div>
            </div>

            <div className="text-right">
              <div className={`text-3xl font-bold mb-1 ${
                theme === 'light' ? 'text-gray-900' : 'text-white'
              }`}>
                {timeUntilUpdate}
              </div>
              <div className="flex items-center justify-end">
                <div className={`w-2 h-2 rounded-full mr-2 animate-pulse ${
                  theme === 'light' ? 'bg-green-500' : 'bg-green-400'
                }`}></div>
                <span className={`text-xs font-semibold ${
                  theme === 'light' ? 'text-green-700' : 'text-green-300'
                }`}>
                  LIVE
                </span>
              </div>
            </div>
          </div>

          <div className={`mt-4 pt-4 border-t ${
            theme === 'light' ? 'border-green-200' : 'border-green-700'
          }`}>
            <div className="flex items-center justify-between text-sm">
              <span className={`${
                theme === 'light' ? 'text-gray-600' : 'text-gray-400'
              }`}>
                Last updated:
              </span>
              <span className={`font-medium ${
                theme === 'light' ? 'text-gray-700' : 'text-gray-300'
              }`}>
                {lastUpdateTime || 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Player Rankings Section */}
        {topPlayers.length > 0 && (
          <div className={`relative overflow-hidden rounded-2xl transition-all duration-300 ${
            theme === 'light'
              ? 'bg-gradient-to-br from-gray-50 to-white'
              : 'bg-gradient-to-br from-gray-700/50 to-gray-800/50'
          }`}>
            {/* Rankings Header */}
            <div className={`px-6 py-4 border-b ${
              theme === 'light' ? 'border-gray-200' : 'border-gray-600'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg mr-3 ${
                    theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
                  }`}>
                    <FaUsers className="text-lg" />
                  </div>
                  <div>
                    <h4 className={`text-lg font-bold ${
                      theme === 'light' ? 'text-gray-900' : 'text-white'
                    }`}>
                      Top Performers
                    </h4>
                    <p className={`text-sm ${
                      theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      Live rankings
                    </p>
                  </div>
                </div>

                {/* Tab Selector */}
                <div className={`flex rounded-lg p-1 ${
                  theme === 'light' ? 'bg-gray-200' : 'bg-gray-600'
                }`}>
                  {(['killpoints', 'power', 'deads'] as const).map((tab) => (
                    <button
                      key={tab}
                      onClick={(e) => {
                        e.stopPropagation();
                        setActiveTab(tab);
                      }}
                      className={`px-3 py-1 text-xs font-medium rounded transition-colors ${
                        activeTab === tab
                          ? theme === 'light'
                            ? 'bg-white text-blue-700 shadow'
                            : 'bg-gray-800 text-blue-400 shadow'
                          : theme === 'light'
                            ? 'text-gray-600 hover:text-gray-800'
                            : 'text-gray-300 hover:text-gray-100'
                      }`}
                    >
                      {tab === 'killpoints' ? 'KP' : tab === 'power' ? 'Power' : 'Deads'}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Top 5 Players List */}
            <div className="p-6">
              <div className="space-y-3">
                {topPlayers.slice(0, 5).map((player, index) => {
                  const rank = index + 1;
                  const isTopThree = rank <= 3;

                  return (
                    <div
                      key={player.governorId}
                      className={`flex items-center justify-between p-3 rounded-xl transition-all duration-200 ${
                        isTopThree
                          ? theme === 'light'
                            ? 'bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200'
                            : 'bg-gradient-to-r from-yellow-900/20 to-amber-900/20 border border-yellow-700'
                          : theme === 'light'
                            ? 'bg-gray-50 hover:bg-gray-100'
                            : 'bg-gray-700/50 hover:bg-gray-600/50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {/* Rank */}
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-sm ${
                          rank === 1
                            ? 'bg-yellow-500 text-white'
                            : rank === 2
                            ? 'bg-gray-400 text-white'
                            : rank === 3
                            ? 'bg-amber-600 text-white'
                            : theme === 'light'
                            ? 'bg-gray-200 text-gray-700'
                            : 'bg-gray-600 text-gray-300'
                        }`}>
                          {rank === 1 ? <FaTrophy className="text-xs" /> :
                           rank === 2 ? <FaMedal className="text-xs" /> :
                           rank === 3 ? <FaAward className="text-xs" /> :
                           rank}
                        </div>

                        {/* Player Info */}
                        <div>
                          <div className={`font-semibold ${
                            theme === 'light' ? 'text-gray-900' : 'text-white'
                          }`}>
                            {player.name}
                          </div>
                          {player.alliance && (
                            <div className={`text-xs ${
                              theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              {player.alliance}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Value */}
                      <div className="text-right">
                        <div className={`font-bold ${
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        }`}>
                          {activeTab === 'killpoints' && formatLargeNumber(player.killPoints || 0)}
                          {activeTab === 'power' && formatLargeNumber(player.power || 0)}
                          {activeTab === 'deads' && formatLargeNumber(player.deads || 0)}
                        </div>
                        <div className={`text-xs font-mono ${
                          theme === 'light' ? 'text-gray-500' : 'text-gray-400'
                        }`}>
                          ID: {player.governorId}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* View All Button */}
              <div className="mt-4 text-center">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/kvk/${kvk.id}`);
                  }}
                  className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    theme === 'light'
                      ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      : 'bg-blue-900 text-blue-300 hover:bg-blue-800'
                  }`}
                >
                  <FaEye className="mr-2" />
                  View Full Rankings
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KvKDashboard;
