import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

// Define user roles
export enum UserRole {
  ADMIN = 'admin',
  OFFICER = 'officer',
  MEMBER = 'member',
  GUEST = 'guest'
}

// User interface
export interface User {
  id: string;
  username: string;
  role: UserRole;
  alliance?: string;
  governorId?: string;
}

// Context type
interface UserContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  canUploadScans: () => boolean;
  canViewAdminPages: () => boolean;
  canManageKvK: () => boolean;
}

// Create context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Production authentication - users managed via backend API

// Provider component
export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(() => {
    if (typeof window !== 'undefined') {
      const savedUser = localStorage.getItem('user');
      const parsedUser = savedUser ? JSON.parse(savedUser) : null;
      console.log('[UserContext] Initial user state:', parsedUser);
      return parsedUser;
    }
    return null;
  });

  const isAuthenticated = user !== null;

  // Save user to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        localStorage.removeItem('user');
      }
    }
  }, [user]);

  // Production login function
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // Make API call to authenticate user
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (response.ok) {
        const userData = await response.json();
        const authenticatedUser: User = {
          id: userData.id,
          username: userData.username,
          role: userData.role as UserRole,
          alliance: userData.alliance,
          governorId: userData.governor_id
        };

        setUser(authenticatedUser);
        localStorage.setItem('user', JSON.stringify(authenticatedUser));
        return true;
      }

      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  // Logout function
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  // Permission checking functions
  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    switch (permission) {
      case 'upload_scans':
        return [UserRole.ADMIN].includes(user.role);
      case 'view_admin_pages':
        return [UserRole.ADMIN].includes(user.role);
      case 'manage_kvk':
        return [UserRole.ADMIN].includes(user.role);
      // Everyone can view dashboards and performance data
      case 'view_dashboards':
        return true;
      default:
        return false;
    }
  };

  const canUploadScans = () => hasPermission('upload_scans');
  const canViewAdminPages = () => hasPermission('view_admin_pages');
  const canManageKvK = () => hasPermission('manage_kvk');

  return (
    <UserContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated,
      hasPermission,
      canUploadScans,
      canViewAdminPages,
      canManageKvK
    }}>
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
