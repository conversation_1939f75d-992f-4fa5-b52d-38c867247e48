import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useQuery } from '@tanstack/react-query';
import { getKvKList, getScans } from '../api/api';
import { formatLargeNumber, formatDate } from '../utils/formatters';
import {
  FaHistory,
  FaSearch,
  FaPlus,
  FaEye,
  FaCrosshairs,
  FaSkullCrossbones,
  FaTrophy,
  FaUsers,
  FaCalendarAlt,
  FaShieldAlt,
  FaExclamationTriangle
} from 'react-icons/fa';

const KvKHistoryPage: React.FC = () => {
  const { theme } = useTheme();
  const { canManageKvK } = useUser();
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch KvK data
  const { data: kvkList = [], isLoading: isLoadingKvK } = useQuery({
    queryKey: ['kvkList'],
    queryFn: getKvKList
  });

  // Fetch all scans
  const { data: allScans = [], isLoading: isLoadingScans } = useQuery({
    queryKey: ['scansList'],
    queryFn: getScans
  });

  // Filter completed and active KvKs (for history viewing)
  const completedKvKs = kvkList.filter(kvk => kvk.status === 'completed' || kvk.status === 'active');

  // Filter KvKs based on search term
  const filteredKvKs = completedKvKs.filter(kvk =>
    kvk.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kvk.season.toString().includes(searchTerm)
  );

  // Sort KvKs by season (newest first)
  const sortedKvKs = [...filteredKvKs].sort((a, b) => b.season - a.season);

  // Get scans for a KvK
  const getKvKScans = (kvkId: string) => {
    return allScans.filter(scan => scan.kvkId === kvkId);
  };

  // Get KvK statistics (simplified for now)
  const getKvKStats = (kvkId: string) => {
    const kvkScans = getKvKScans(kvkId);

    // For now, return basic stats since scan data doesn't include player details
    // TODO: Integrate with performance summary API for detailed stats
    return {
      totalKillPoints: kvkScans.length > 1 ? 'TBD' : 0,
      totalDeads: kvkScans.length > 1 ? 'TBD' : 0,
      totalT45Kills: kvkScans.length > 1 ? 'TBD' : 0,
      playerCount: kvkScans.length > 0 ? 'Available' : 0
    };
  };

  // Loading state
  if (isLoadingKvK || isLoadingScans) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className={`text-center ${theme === 'light' ? 'text-gray-600' : 'text-gray-300'}`}>
          Loading KvK history...
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'light' ? 'bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50' : 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900'
    }`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Enhanced Header */}
        <div className={`relative overflow-hidden rounded-3xl mb-8 ${
          theme === 'light'
            ? 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-100'
            : 'bg-gradient-to-br from-gray-800 via-blue-900 to-indigo-900 border border-gray-700'
        }`}>
          <div className="relative px-8 py-12">
            <div className="flex items-center mb-4">
              <div className={`p-3 rounded-xl mr-4 ${
                theme === 'light' ? 'bg-blue-100 text-blue-600' : 'bg-blue-900 text-blue-400'
              }`}>
                <FaHistory className="text-2xl" />
              </div>
              <div>
                <h1 className={`text-4xl font-bold ${
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  KvK History
                </h1>
                <p className={`text-lg mt-2 ${
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Complete archive of Kingdom 2358's battle history and achievements
                </p>
              </div>
            </div>

            {/* Stats Summary */}
            <div className="flex items-center space-x-6 mt-6">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  theme === 'light' ? 'bg-blue-500' : 'bg-blue-400'
                }`}></div>
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.length} Total KvKs
                </span>
              </div>
              <div className="flex items-center">
                <FaShieldAlt className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-green-600' : 'text-green-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.filter(kvk => kvk.status === 'active').length} Active
                </span>
              </div>
              <div className="flex items-center">
                <FaTrophy className={`text-sm mr-2 ${
                  theme === 'light' ? 'text-yellow-600' : 'text-yellow-400'
                }`} />
                <span className={`text-sm font-medium ${
                  theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                }`}>
                  {sortedKvKs.filter(kvk => kvk.status === 'completed').length} Completed
                </span>
              </div>
            </div>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 opacity-10">
            <FaHistory className="w-full h-full transform rotate-12" />
          </div>
        </div>

      {/* Search and Controls */}
      <div className="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="w-full md:w-1/3">
          <input
            type="text"
            placeholder="Search KvK by name or season..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full px-4 py-2 rounded-md border ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-700 text-white'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          />
        </div>

        {canManageKvK() && (
          <Link
            to="/create-kvk"
            className={`px-4 py-2 rounded-md ${
              theme === 'light'
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-blue-700 text-white hover:bg-blue-600'
            }`}
          >
            Create New KvK
          </Link>
        )}
      </div>

      {/* KvK History Cards */}
      {sortedKvKs.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedKvKs.map(kvk => {
            const stats = getKvKStats(kvk.id);
            return (
              <div
                key={kvk.id}
                className={`rounded-lg shadow-md overflow-hidden ${
                  theme === 'light' ? 'bg-white' : 'bg-gray-800'
                }`}
              >
                <div className={`px-4 py-3 ${theme === 'light' ? 'bg-blue-50' : 'bg-blue-900/20'}`}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className={`font-semibold ${theme === 'light' ? 'text-blue-700' : 'text-blue-300'}`}>
                        {kvk.name}
                      </h3>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                        Season {kvk.season}
                      </p>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      kvk.status === 'active'
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : kvk.status === 'completed'
                        ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                    }`}>
                      {kvk.status.charAt(0).toUpperCase() + kvk.status.slice(1)}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Start Date
                      </p>
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {formatDate(kvk.startDate)}
                      </p>
                    </div>
                    <div>
                      <p className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        End Date
                      </p>
                      <p className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {kvk.endDate ? formatDate(kvk.endDate) : 'N/A'}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Kill Points
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalKillPoints === 'number' ? formatLargeNumber(stats.totalKillPoints) : stats.totalKillPoints}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Dead Troops
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalDeads === 'number' ? formatLargeNumber(stats.totalDeads) : stats.totalDeads}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        T4-5 Kills
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {typeof stats.totalT45Kills === 'number' ? formatLargeNumber(stats.totalT45Kills) : stats.totalT45Kills}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className={`text-sm ${theme === 'light' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Players
                      </span>
                      <span className={`font-medium ${theme === 'light' ? 'text-gray-800' : 'text-white'}`}>
                        {stats.playerCount}
                      </span>
                    </div>
                  </div>

                  <Link
                    to={`/kvk/${kvk.id}`}
                    className={`block w-full text-center px-4 py-2 rounded-md ${
                      theme === 'light'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-blue-700 text-white hover:bg-blue-600'
                    }`}
                  >
                    View Details
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className={`p-8 rounded-lg border text-center ${
          theme === 'light' ? 'bg-gray-50 border-gray-200 text-gray-500' : 'bg-gray-800 border-gray-700 text-gray-400'
        }`}>
          {searchTerm ? 'No KvKs match your search criteria.' : 'No KvKs available yet.'}
        </div>
      )}
      </div>
    </div>
  );
};

export default KvKHistoryPage;
