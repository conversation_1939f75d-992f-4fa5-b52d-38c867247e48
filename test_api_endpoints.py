#!/usr/bin/env python3
"""
Simple test script to check API endpoints and data flow
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, description):
    """Test a single endpoint and print results"""
    print(f"\n{'='*50}")
    print(f"Testing: {description}")
    print(f"Endpoint: {endpoint}")
    print(f"{'='*50}")
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response Type: {type(data)}")
            
            if isinstance(data, dict):
                print(f"Keys: {list(data.keys())}")
                for key, value in data.items():
                    if isinstance(value, list):
                        print(f"  {key}: list with {len(value)} items")
                        if len(value) > 0:
                            print(f"    First item type: {type(value[0])}")
                            if isinstance(value[0], dict):
                                print(f"    First item keys: {list(value[0].keys())}")
                    elif isinstance(value, dict):
                        print(f"  {key}: dict with keys {list(value.keys())}")
                    else:
                        print(f"  {key}: {type(value).__name__} = {value}")
            elif isinstance(data, list):
                print(f"List with {len(data)} items")
                if len(data) > 0:
                    print(f"First item type: {type(data[0])}")
                    if isinstance(data[0], dict):
                        print(f"First item keys: {list(data[0].keys())}")
            
            # Print first few characters of response for debugging
            response_text = json.dumps(data, indent=2)
            if len(response_text) > 500:
                print(f"Response preview (first 500 chars):\n{response_text[:500]}...")
            else:
                print(f"Full response:\n{response_text}")
                
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("ERROR: Could not connect to server. Make sure the backend is running on port 8000.")
    except Exception as e:
        print(f"ERROR: {str(e)}")

def main():
    """Test all relevant endpoints"""
    print("API Endpoint Testing Script")
    print("Make sure the backend server is running on port 8000")
    
    # Test basic endpoints
    test_endpoint("/api/scans/", "Get all scans")
    test_endpoint("/api/kvks/", "Get all KvKs")
    test_endpoint("/api/dashboard/", "Get dashboard data")
    test_endpoint("/api/reports/general/performance_summary?limit=10", "Get general performance summary")
    
    print(f"\n{'='*50}")
    print("Testing complete!")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
