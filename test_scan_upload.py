#!/usr/bin/env python3
"""
Test script to verify scan upload functionality
"""
import requests
import json
import io

# Test data - simple CSV content
csv_content = """name,power,total_kill_points,dead_troops,alliance,governor_id
TestPlayer1,50000000,1000000,500000,TestAlliance,12345
TestPlayer2,45000000,800000,400000,TestAlliance,12346
TestPlayer3,60000000,1200000,600000,TestAlliance2,12347
"""

def test_scan_upload():
    """Test the scan upload endpoint"""
    url = "http://127.0.0.1:8000/api/scans/upload"

    import time
    timestamp = int(time.time())

    # Prepare the file upload
    files = {
        'file': ('test_scan.csv', io.StringIO(csv_content), 'text/csv')
    }

    params = {
        'scan_name': f'test_baseline_{timestamp}',
        'is_baseline': 'true'
    }

    try:
        print("Testing scan upload...")
        response = requests.post(url, files=files, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ Scan upload successful!")
            return response.json()
        else:
            print("❌ Scan upload failed!")
            return None

    except Exception as e:
        print(f"❌ Error testing scan upload: {e}")
        return None

def test_kvk_creation():
    """Test KvK creation"""
    url = "http://127.0.0.1:8000/api/kvks/"

    import time
    timestamp = int(time.time())

    data = {
        "name": f"Test KvK {timestamp}",
        "start_date": "2024-01-01T00:00:00",
        "season": 1,
        "status": "active"
    }

    try:
        print("Testing KvK creation...")
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 201:
            print("✅ KvK creation successful!")
            return response.json()
        else:
            print("❌ KvK creation failed!")
            return None

    except Exception as e:
        print(f"❌ Error testing KvK creation: {e}")
        return None

def test_kvk_performance(kvk_id):
    """Test KvK performance summary"""
    url = f"http://127.0.0.1:8000/api/reports/kvk/performance_summary"

    params = {
        'kvk_id': kvk_id,
        'limit': 50
    }

    try:
        print("Testing KvK performance summary...")
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ KvK performance summary successful!")
            return response.json()
        else:
            print("❌ KvK performance summary failed!")
            return None

    except Exception as e:
        print(f"❌ Error testing KvK performance: {e}")
        return None

if __name__ == "__main__":
    print("🧪 Testing backend API fixes...")
    print("=" * 50)

    # Test 1: Create a KvK
    kvk_data = test_kvk_creation()

    if kvk_data:
        kvk_id = kvk_data.get('id')
        print(f"Created KvK with ID: {kvk_id}")

        # Test 2: Upload a scan
        scan_data = test_scan_upload()

        if scan_data:
            # Test 3: Get KvK performance summary
            performance_data = test_kvk_performance(kvk_id)

            if performance_data:
                print("🎉 All tests passed!")
            else:
                print("⚠️ KvK performance test failed")
        else:
            print("⚠️ Scan upload test failed")
    else:
        print("⚠️ KvK creation test failed")

    print("=" * 50)
    print("Test completed!")
